:root {
    --thermarate-green: #8bc34a;
    --thermarate-green-light: rgba(139, 195, 74, 0.47);
    --thermarate-grey: #eeeeee;

    --warning: #f8502a;

    --card-border-radius: 10px;
    --background-grey: #eeeeee;
    --widget-background-grey: rgba(0,0,0,0.05);
    --color-text-dark: #656565;

    --hidden-render-opacity: 25%;
}

html {
  font-size: 62.5%;
  line-height: 1.4;
  background-color: white;
}
body {
  font-size: 1.2rem;
}
.md-autocomplete-suggestions li{font-size:12px;line-height:38px;height:38px;}
md-option { height: 38px; }
/*
    Without this auto-complete dropdowns match the width of their auto-complete parent
    .md-virtual-repeat-container.md-autocomplete-suggestions-container { min-width: 350px !important; }
*/
.md-headline { font-size:20px; line-height: 28px; }

.content-center {
    text-align:center;
}

/* Assessment Quantity padding */
.absv-quantity {
    margin-right: 4em;
}

/*Allows floating label look and feel while having no text in label*/
md-autocomplete.no-label > md-autocomplete-wrap > md-input-container > label {
  display: none;
}

.template-modal-footer {
    background-color: rgba(0,0,0,0.03);
    border-top: 1px solid rgba(0,0,0,0.12);
}

.compass-point {
    cursor:pointer;
    outline: none;
}

.compass-container {
    width: 150px;
    height: 150px;
    position: relative;
}

img.compass-needle {
    width: 35%;
    height: auto;
    position: absolute;
    z-index: 2;
}

.compass-svg {
    position: absolute;
    z-index: 3;
}

.md-button.md-small {
  width: 20px;
  padding-left:0px;
  padding-right:0px;
  margin-left:0px;
  margin-right:0px;

}

.red {
    color:red;
}

md-input-container.actionbar-dropdown {
    width:300px;
    font-size:1.2rem;
    color:rgba(0, 0, 0, 0.870588);
}

md-input-container.actionbar-dropdown > md-select {
        color:rgba(0, 0, 0, 0.870588) !important;
}

md-input-container.actionbar-dropdown > md-select {
    margin-bottom:0px;
}

md-input-container.actionbar-dropdown > md-select:not([disabled]):focus md-select-value {
    border-bottom-color:rgba(0,0,0,0.870588);
    border-bottom-width:1px;
}

md-input-container.small-input {
    width:60px;
}

md-input-container.small-input .md-input {
    width:60px;
}

md-input-container .md-input.no-line {
    padding-bottom:2px;
    border-bottom:0px;
}

.important-field {
    border: none;
    outline: none;
}

.important-field > label {
    outline: none;
    border: none;
}

.important-field :focus {
    outline: none;
    border: none;
}

.important-field.highlight {
    background-color: #e8f3de;
    border: none;
    outline: none;
}

.no-focus:focus {
    outline: none;
    border: none;
}

.no-padding-table > tbody > tr > td {
    vertical-align:top;
}

.file-dragover {
    background-color:rgba(139,195,74,0.5) !important;
}

.row-invalid > td {
    background-color:rgba(255,0,0,0.1) !important;
}

.comment-content {
    padding-left:20px;
    padding-bottom:10px;
    white-space:pre-line;
}

.comment-header-unimportant {
    opacity:0.7;
}

.record-failed {
    background-color: red;
}

.record-complete {
    background-color: green;
}

.record-pending {
    background-color: yellow;
}

.record-retry {
    background-color: orange;
}

.queue-status-colour {
    width: 10px;
    height: 100%;
    display: table-cell;
}

/* Unauthorised page*/
.unauthorised {
    padding-top: 50px;
    margin: 0 auto;
    text-align: center;
    font-size: 50px;
    font-weight: bolder;
    color: black;
}

/* Exclude from hamburger @ 1024 px*/
@media screen and (max-width: 1024px) {
    /* Custom Widget Action Toolbar */
    .find-text-box {
        font-size: 10px !important;
    }
}


/*upload care nooying so very precise styling*/
.uploadcare-widget-button.uploadcare-widget-button-open {
    background-color: #01a7e1 !important;
}

    /*upload care nooying so very precise styling*/
    .uploadcare-widget-button.uploadcare-widget-button-open:hover {
        background-color: #188D8B !important;
    }

.upload-panel {
    min-height: 200px;
}

.panel-primary {
    border: 1px solid rgb(139,195,74);
    border-radius: 4px;
}

.panel-primary > .panel-heading {
    border-color: rgb(139,195,74);
    background-color: rgb(139,195,74);
}

.panel-primary > .panel-heading {
    color: #fff;
    background-color: rgb(139,195,74);
    border-color: rgb(139,195,74);
}

.panel-heading {
    padding: 10px 15px;
    border-bottom: 1px solid transparent;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
}

.panel-success {
    border-color: green;
}

.panel-success > .panel-heading {
    border-color: green;
    background-color: green;
    color: white;
}

.upload-data-section {
    padding-bottom: 20px;
}

.panel-body {
    padding: 15px;
}


.ng-cloak {
    display: none !important;
}

small {
    font-size: 11px;
}

.text-subtle {
    color: #b7b7b7;
}

.table > tbody > tr > td.content-center, .table > thead:first-child > tr:first-child > th.content-center {
    text-align: center;
}
.page-title-subtle {
    color: #0055a5;
}

h2 span.page-title-subtle {
    color: rgb(139,195,74);
    font-size: 14px;
    padding-left: 20px;
}

.page-title {
    color: #08405e;
    font-weight: 600;
    font-size: 22px;
    margin-right: 12px;
}

.new-job-nav {
    color: rgb(139,195,74)!important;
}

.page-title-subtlenew {
    color: #08405e;
    font-weight: 600;
    font-size: 16px;
    margin-right: 12px;
    margin-left: 5px;
    margin-top: 25px;
    margin-bottom: 25px;
}

.main-content-wrapper {
    margin: auto;
    max-width: 1770px;
}
.md-dialog-container .main-content-wrapper {
    margin : 0;
}
md-dialog {
    max-height: 95%;
}

.widget-content .padd:hover {
    cursor: pointer;
    color: white;
    background-color: #52b9e9;
}

.widget-content .padd:hover * {
    color: white;
}

div.modal-content-wrapper md-card {
    box-shadow:none;
}
div.modal-content-wrapper md-card md-card-header {
    display:none;
}

div.modal-content-wrapper > md-tabs.hide-tabs-in-modal md-tabs-wrapper {
    display: none;
}

/* Custom Widget Action Toolbar */
.widget-actionbar-wrapper {
    display: flex;
    display: -webkit-flex;
    background-color: var(--background-grey) !important;
}

.left-menu-button {
    text-align: left !important;
    align-items: flex-start !important;
    width: 100% !important;
}

.widget {
    background: none !important;
}

.widget .widget-head {
    border: none !important;
    background-color: none !important;
}

.widget .widget-actionbar {
}

.widget .widget-actionbar .date-range-picker,
.action-bar-date-filter-wrapper .date-range-picker {
    width: auto;
    line-height: 0px;
    padding: 8px;
    margin-right: -8px;
}

md-toolbar {
    min-height: unset !important;
}
.widget .widget-actionbar .md-toolbar-tools {
    height: 42px;
    max-height: 42px;
    margin-top: 4px;
}

.text-left {
    text-align:left;
}

.text-center {
    text-align: center;
}

.quick-search {
    /*background: none;
    border: 2px solid white !important;
    border-radius: 0;*/
    border:none;
    border-radius: 10px;
    background-color: var(--widget-background-grey) !important;
    padding-left:10px;
    height: 33px;
    min-width: 240px;
    width: 240px;
    margin: 0 0 !important;
    font-size: 15px;
    font-family: robotoregular2;
}

.widget .widget-actionbar select {
    background-position: right center;
    background-color: rgba(0,0,0,0.1) !important;
    /*background-image: url("../content/images/white-updown-arrows.png") !important;*/
    border: none !important;
    width: auto;
    display: inline-block;
    font-family: robotolight;
    font-size: initial !important;
    padding: 5px 15px !important;
}

.action-button-right {
    flex: 1 1 75%;
    text-align: right;
}

.refresh-btn-container {
    width: 4%;
    text-align: center;
    vertical-align: middle;
    position: relative;
    height: auto; /*47px;*/
    display: flex;
    display: -webkit-flex;
}

.refresh-btn-container .spinner-right {
    position: absolute;
    right: 0;
    left: 0;
    top:-8px;
    vertical-align: middle;
    margin: auto;
    padding: 0;
}

.filter-picker-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
    white-space: nowrap;
}
.filter-dropdown {
    margin: 7px 0;
}
.filter-dropdown .md-select-value {
    border-bottom: none !important;
}

.no-line-height {
    line-height: 0px;
}

.customFiltersBar {
    height: 28px;
    min-height: 28px;
    margin-top: 0px;
    margin-left: 0px;
    margin-right: 0px;
    padding-top: 5px;
    padding-left: 5px;
    padding-right: 5px;
    background-color: white !important;
    font-size: 1.2rem;
    color: #757575;
    line-height: 1em;
}

md-toolbar.customFiltersBar > div.customFilter:hover {
    text-decoration: line-through !important;
}

.columnFilter {
    font-size: 16px;
    margin: 0;
    padding-left: 1em;
    padding-right: 1em;
}

div.columnFilter > md-checkbox {
    margin-bottom: 0.5em;
}

.filterButton {
    width: 42px;
    height: 42px;
    margin-left: 0.5em;
    margin-right: 0.5em;
}

.activeFilter {
    font-size: 48px !important;
}

.customFiltersBar .blueCustomFilter {
    color: #69f;
    font-weight: 400;
}

.customFiltersBar .blackCustomFilter {
    color: black;
    font-weight: bold;
}

input.quick-search::-webkit-input-placeholder {
    color: #222222 !important;
    font-family: robotolight;
}

input.quick-search:-moz-placeholder { /* Firefox 18- */
    color: #222222 !important;
    font-family: robotolight;
}

input.quick-search::-moz-placeholder { /* Firefox 19+ */
    color: #222222 !important;
    font-family: robotolight;
}

input.quick-search:-ms-input-placeholder {
    color: #222222 !important;
    font-family: robotolight;
}

.quick-search {
    margin-right: 4px;
}

.widget-search-input-container {
    position: relative;
    display: inline-block;
}

.widget-search-clear-button {
    position: absolute;
    right: 6px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 50;
    width: 18px;
    height: auto;
    padding: 4px;
    border-radius: 50%;
    cursor: pointer;
}

.widget-search-clear-button:hover {
    background-color: #eeeeee;
}

    .quick-search::-webkit-input-placeholder::before {
        font-family: fontAwesome;
        content: '\f002';
        color: #69f;
    }

    .quick-search::-moz-placeholder::before {
        font-family: fontAwesome;
        content: '\f002';
        color: #69f;
    }

:-ms-input-placeholder.quick-search::before {
    font-family: fontAwesome;
    content: '\f002';
    color: #69f;
}

input:-moz-placeholder.quick-search::before {
    font-family: fontAwesome;
    content: '\f002';
    color: #69f;
}

.search-query {
    margin: 2px auto 8px auto !important;
}

.empty-list-message {
    color: #515151;
}

.form-search {
    min-height: 2em;
}

.user .user-pic {
    display: block;
    margin: auto;
}

.user .user-details {
    margin-left: 100px;
    margin-right: 10px;
}

.date-range-picker {
    font-family: "Proxima Nova Regular", 'Helvetica Neue', Arial, Helvetica, sans-serif;
    font-size: 15px;
    line-height: 18px;
    color: #333333;
    /*padding: 8px;*/
    cursor: pointer;
    /*max-width: 360px;*/
}

    .date-range-picker span {
        padding-left: 6px;
    }

    .date-range-picker .caret {
        margin-top: 8px;
        margin-left: 6px;
    }

    .date-range-picker .well {
        margin-bottom: 0;
    }
    .date-range-picker.well {
        display: flex !important;
        align-items: center;
    }

.daterangepicker.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 160px;
    padding: 5px 0;
    margin: 2px 0 0;
    font-size: 14px;
    list-style: none;
    background-color: #fff;
    -webkit-background-clip: padding-box;
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,.15);
    border-radius: 4px;
    -webkit-box-shadow: 0 6px 12px rgba(0,0,0,.175);
    box-shadow: 0 6px 12px rgba(0,0,0,.175);
}

.daterangepicker .table-condensed > tbody > tr > td {
    padding: 5px 5px 2px 5px;
}

.daterangepicker .calendar th, .daterangepicker .calendar td  {
    min-width:19px !important;
}

.daterangepicker.opensright:after, .daterangepicker.opensright:before {
    display:none !important;
}

.report-filters .nav.nav-pills {
    padding-top: 8px;
}

.report-filters .nav > li > a {
    padding-top: 7px;
    padding-bottom: 7px;
}

.report-filters .nav-pills > li:not(.active) a {
    background-color: #0055a5;
    color: white;
}

.report-filters .nav-pills > li.active > a {
    font-weight: bold;
}

.report-filters .nav-pills > li:not(.active) a:hover {
    background-color: #e1e1e1;
}

.report-box-item .alert {
    font-size: 22px;
}

.report-box-item span {
    font-size: 15px;
}

.widget-content {
    padding: 0;
}

thead > tr > th.action-col {
    width:60px;
    max-width:60px;
}
tbody > tr > td > a.md-button {
    width: 60px;
    min-width: 60px;
}

/* Modal Dialog */
.smallModal {
     max-width: 380px;
}

/* This is based on Material Angular Dialog Action Bar */
.modal-footer {
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    -webkit-box-ordinal-group: 3;
    -webkit-order: 2;
    order: 2;
    box-sizing: border-box;
    -webkit-box-align: center;
    -webkit-align-items: center;
    align-items: center;
    -webkit-box-pack: end;
    -webkit-justify-content: flex-end;
    justify-content: flex-end;
    margin-bottom: 0;
    padding-right: 8px;
    padding-left: 16px;
    min-height: 52px;
    overflow: hidden;
    background-color: rgba(0,0,0,.03);
    border-top: 1px solid rgba(0,0,0,.12);
}

.stacked {
    float: none !important;
    display: block;
    margin: auto;
}

.today-datas li {
    padding: 20px 14px;
}

.badge {
    font-size: 11px !important;
    font-weight: 300;
    text-align: center;
    background-color: #e02222;
    height: 18px;
    padding: 3px 6px 3px 6px;
    -webkit-border-radius: 12px !important;
    -moz-border-radius: 12px !important;
    border-radius: 12px !important;
    text-shadow: none !important;
    text-align: center;
    vertical-align: middle;
}

.badge-help {
    font-size: 11px !important;
    font-weight: 500;
    text-align: center;
    background-color: #428bca;
    height: 18px;
    padding: 3px 6px 3px 6px;
    -webkit-border-radius: 12px !important;
    -moz-border-radius: 12px !important;
    border-radius: 12px !important;
    text-align: center;
    vertical-align: middle;
    color: white;
}

.helpBlue {
    font-size: 11px !important;
    font-weight: 500;
    text-align: center;
    background-color: #428bca;
    height: 18px;
    padding: 3px 6px 3px 6px;
    -webkit-border-radius: 12px !important;
    -moz-border-radius: 12px !important;
    border-radius: 12px !important;
    text-align: center;
    vertical-align: middle;
    color: white;
}

.helpOrange {
    font-size: 11px !important;
    font-weight: 500;
    text-align: center;
    background-color: orange;
    height: 18px;
    padding: 3px 6px 3px 6px;
    -webkit-border-radius: 12px !important;
    -moz-border-radius: 12px !important;
    border-radius: 12px !important;
    text-align: center;
    vertical-align: middle;
    color: white;
}
/*#region Splash */
#splash-page {
    z-index: 99999 !important;
}

    #splash-page .bar {
        width: 100%;
    }

.page-splash {
    z-index: 99999 !important;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #ffffff;
    opacity: .9;
    pointer-events: auto;
    -webkit-backface-visibility: hidden;
    -moz-backface-visibility: hidden;
    -ms-backface-visibility: hidden;
    -o-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-transition: opacity 0.3s linear;
    -moz-transition: opacity 0.3s linear;
    -o-transition: opacity 0.3s linear;
    transition: opacity 0.3s linear;
}

.page-splash-message {
    height: 100%;
    /* This must be a base64 encoded image */
    background: url('data:image/png;base64,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') no-repeat center;
}

    .page-splash-message.page-splash-message-subtle {
        margin: 30% auto 0 auto;
        font-size: 200%;
    }

.icon-asterisk.icon-asterisk-large {
    font-size: 180%;
    vertical-align: middle;
    color: #F58A00;
}

.icon-asterisk.icon-asterisk-alert {
    color: #F58A00;
}

.icon-asterisk-inline {
    padding: 0 4px 0 0;
}


/*.progress,*/

.spinner {
    margin: 10px auto 0 auto;
    left: auto;
    top: auto !important;
}

.spinner-mid-page {
    position: fixed;
    top: 40%;
    left: 50%;
}

/*#endregion*/
a {
    cursor: pointer;
}

/* #endregion */

/*#region wrapper for angular ng-include and ng-view animations*/
.view-container {
    position: relative;
    overflow: hidden;
}
/*#endregion */

/*#region Angular ng-include, ng-view, ng-repeat shuffle animations*/

.shuffle-animation.ng-enter,
.shuffle-animation.ng-leave {
    position: relative;
}

.shuffle-animation.ng-enter {
    -moz-transition: ease-out all 0.1s 0.1s;
    -o-transition: ease-out all 0.1s 0.1s;
    -webkit-transition: ease-out all 0.1s 0.1s;
    transition: ease-out all 0.1s 0.1s;
    left: 2em;
    opacity: 0;
}

    .shuffle-animation.ng-enter.ng-enter-active {
        left: 0;
        opacity: 1;
    }

.shuffle-animation.ng-leave {
    -moz-transition: 0.1s ease-out all;
    -o-transition: 0.1s ease-out all;
    -webkit-transition: 0.1s ease-out all;
    transition: 0.1s ease-out all;
    left: 0;
    opacity: 1;
}

    .shuffle-animation.ng-leave.ng-leave-active {
        left: 2em;
        opacity: 0;
    }
/*#endregion*/

/*#region Angular ng-include, ng-view, ng-repeat fader animation */
.fader-animation.ng-enter,
.fader-animation.ng-leave,
.fader-animation.ng-move {
    /*position: relative;*/
}

.fader-animation.ng-enter,
.fader-animation.ng-leave {
    -webkit-transition: cubic-bezier(0.250, 0.460, 0.450, 0.940) all 0.5s;
    -moz-transition: cubic-bezier(0.250, 0.460, 0.450, 0.940) all 0.5s;
    -o-transition: cubic-bezier(0.250, 0.460, 0.450, 0.940) all 0.5s;
    transition: cubic-bezier(0.250, 0.460, 0.450, 0.940) all 0.5s;
    opacity: 1;
}

    .fader-animation.ng-enter,
    .fader-animation.ng-leave.ng-leave-active {
        opacity: 0;
    }

        .fader-animation.ng-enter.ng-enter-active {
            opacity: 1;
        }

.fader-animation.ng-move {
    opacity: 0.5;
}

    .fader-animation.ng-move.ng-move-active {
        opacity: 1;
    }

/*#endregion*/

/*#region Angular ng-show dissolve animation */
.dissolve-animation.ng-hide-remove {
    display: inline !important;
}

.dissolve-animation.ng-hide-remove,
.dissolve-animation.ng-hide-add {
    -webkit-transition: 0.8s linear all;
    -moz-transition: 0.8s linear all;
    -o-transition: 0.8s linear all;
    transition: 0.8s linear all;
}

    .dissolve-animation.ng-hide-remove.ng-hide-remove-active,
    .dissolve-animation.ng-hide-add {
        opacity: 1;
    }

        .dissolve-animation.ng-hide-add.ng-hide-add-active,
        .dissolve-animation.ng-hide-remove {
            opacity: 0;
        }
/*#endregion */

/*#region toastr */
#toast-container.toast-top-full-width > div, #toast-container.toast-bottom-full-width > div {
    margin: 4px auto;
}
/*#endregion */

/*#region Responsive */

@media (max-width: 979px) {

    .page-splash-message {
        font-size: 300%;
    }
}

@media (max-width: 767px) {

    /* CUSTOM - RSS*/

    .page-splash-message {
        font-size: 200%;
        margin: 40% auto 0 auto;
    }

        .page-splash-message.page-splash-message-subtle {
            font-size: 150%;
        }

    .hide-on-small-screen {
        display: none;
    }

    /* Custom Logsims Classes*/

    .widget .table {
        font-size: 9px;
    }

    .table-filters {
        display: none;
    }

    body {
        padding-top: 45px!important;
    }

}

@media (max-width: 987px) {

    /** Custom Navbar **/

    .settings-image, .settings-image:after {
        top: 3px !important;
    }

    .user-settings {
        height: 50px !important;
    }

    .login-area {
        height: 50px !important;
    }

    .nav-add-button {
        margin-top:6px !important;
    }

    .custom-pixels {
        height: 50px !important;
    }
}

@media (max-width: 320px) {

    .user .user-details {
        margin-left: 7em;
        margin-right: .5em;
    }

    h3 {
        font-size: 1.5em !important;
        line-height: 1.2em !important;
    }
}

.button-fa-green {
    color: #77c76d;
    background: none;
    font-size: 26px !important;
}

    .button-fa-green:hover {
        color: #37c76d;
    }

.button-fa-red {
    color: red;
    background: none;
    font-size: 26px !important;
}

.button-fa-del {
    color: #e1e1e1;
    background: none;
    font-size: 26px !important;
}

.btn-green {
    background-color: green;
    color: white;
    font-weight: 600;
}

.btn-orange {
    background-color: #fed12f;
    color: white;
}

.extra-small-button {
    font-size: 14px !important;
}

.display-parent-hover {
    display: none !important;
}

.parent-hover:hover .display-parent-hover,
li:hover .display-parent-hover {
    display: inline-block !important;
}

.spacing-below {
    margin-bottom: 5px;
}

.big-spacing-below {
    margin-bottom: 10px;
}

.large-spacing-below {
    margin-bottom: 20px;
}

.spacing-above {
    margin-top: 5px;
}

.big-spacing-above {
    margin-top: 10px;
}

.reduced-panel-spacing {
    margin-bottom: 10px;
}

.spacing-left {
    margin-left: 5px;
}

.spacing-small-right {
    margin-right: 4px;
}

.pull-down {
    display: inline-block;
    vertical-align: bottom;
    float: none;
}

.pull-text-bottom {
    vertical-align: text-bottom;
}

.row2-box {
    min-height: 115px;
}

.medium-icon {
    font-size: 25px;
    height: 25px;
    vertical-align: middle;
}

.small-icon {
    font-size: 15px;
    height: 15px;
    vertical-align: middle;
}

a.editable-click {
    border-bottom: none !important;
}

a.editable-click:hover {
    color: #47a447;
}

md-datepicker.hiddenCalendar .md-datepicker-button {
    display: none;
}

md-datepicker.hiddenCalendar .md-datepicker-input-container {
    margin-left: 0;
}

md-checkbox.md-indeterminate .md-icon:after {
    width: 12px;
    height: 12px;
    border-right: 0;
    border-bottom: 0;
    background-color: silver;
}

.md-button.md-icon-button {
    min-width: 40px;
}

md-input-container {
    margin: 9px 0;
}
md-input-container > md-select {
    margin: 0 0 26px 0;
}
md-input-container > md-radio-group {
    margin-bottom: 30px;
}

md-input-container.readonly-data span {
    padding-top: 0px;
    display: block;
    margin-bottom: 9px;
}
md-input-container.readonly-data label {
    transform: scale(0.75) !important;
}

md-input-container span.read-only-field-value {
    padding-top: 0px;
    display: block;
    margin-bottom: 9px;
}

md-dialog {
    min-width: 380px;
}

md-tabs-canvas {
    background-color: white;
}

md-pagination-wrapper md-ink-bar {
    color: rgb(139, 195, 74);
    background-color: rgb(139, 195, 74);
}

@media (max-width: 480px) {
    .widget .widget-actionbar .date-range-picker {
        font-size: 10.5px;
        line-height: normal !important;
    }

    .widget-actionbar .quick-search {
        width: 100% !important;
    }

}
/*#endregion */

.fontlarger {
    font-size: 18px;
    font-weight: bold;
}

.fontmedium {
    font-size: 15px;
}

.fontsmaller {
    font-size: 11px;
    line-height: 15px;
    word-break: break-all;
}

.border-row {
    border-bottom: 0px solid #fff;
    margin-bottom: 6px;
    padding-bottom: 6px;
}

.addbinbutton {
    margin-top: 30px;
}

.table-hover thead tr:hover>th.can-sort {
    cursor: pointer;
}

.st-sort-ascent:after {
    padding-left: 5px;
    content: '\25B2';
}

.st-sort-descent:after {
    padding-left: 5px;
    content: '\25BC';
}

.table-filters th select {
    height: 24px;
    font-size: 12px !important;
    line-height: 24px;
    /*font-weight: normal;*/
    padding: 0px !important;
    color: #000;
    font-weight: 700;
}

.table-filters th input {
    height: 24px;
    font-size: 12px !important;
    line-height: 24px;
    font-weight: normal;
    padding: 0px !important;
    color: #f1b622;
}

.search-choice-clear:after {
    padding-right: 20px;
    float: right;
    color: gray;
    font-family: fontAwesome;
    content: '\f057';
    cursor: pointer;
}

.button-fa-download {
    color: #e1e1e1;
    background: none;
    font-size: 26px !important;
}

.list-boolean-true:before {
    font-family: FontAwesome;
    color: green;
    content: "\f00c";
    padding: 20px;
}

.list-boolean-no:before {
    content: "NO";
    padding: 20px;
}

.list-boolean-attention:before {
    font-family: FontAwesome;
    content: "\f071";
    padding: 20px;
}

.error {
    color: #a94442 !important;
}

.activity-type-title {
    font-size: 20px;
    font-weight: bold;
    padding-left: 20px !important;
    padding-right: 20px !important;
}

.selected {
    background-color: steelblue !important;
    color: white;
    font-weight: bold;
}

    .selected label {
        color: white !important;
    }

.row-space {
    margin-top: 15px;
}

.label-bold {
    font-weight : bold;
}

.label-red {
    color: red;
}

.tab-has-errors,
.card-has-errors {
    color: red;
}

.label-green {
    color: limegreen;
}

.label-orange {
    color:orange;
}

.label-back-green {
    background: #77c76d !important;
    font-weight: normal !important;
}

.label-back-red {
    background: red !important;
    font-weight: normal !important;
}

.left-margin {
    margin-left: 10px;
}

/* Data Report - Just big Wide List */
div.data-report {
    overflow-x: auto;
}

.data-report table {
    display: inline-block;
}

.data-report thead,
.data-report tbody {
    display: inline-block;
}

.data-report thead {
}

.data-report tr td {
}

.data-report tbody {
    overflow-y: scroll;
    max-height: 420px;
}

div.data-report table tr th,
div.data-report table tbody tr td {
    min-width: 150px;
    max-width: 150px;
}

.green-text {
    color: green;
    font-weight: bold;
    text-align: center;
    width: 100%;
}

.comment-icon::before {
    font-family: fontAwesome;
    content: '\f086';
}
.nopadding {
    padding-left: 0px;
    margin-left: 0px;
    padding-right: 0px;
    margin-right: 0px;
}

.full-height {
    height: 100%;
    min-height: 100%;
}

.panel-default > .panel-heading {
    border-radius: 0;
    padding: 0px 15px;
    color: #727171;
    background-color: #ffffff;
    border-color: #ececec;
}

.panel-data {
    background-color: #ededed;
    margin-bottom: 15px;
}

.panel-data > .panel-heading {
    border-radius: 0;
    padding: 5px 15px;
    color: #9e9e9e;
    border-color: #01a7e1;
}

fieldset {
    border: 0;
}

div.event {
    color:#515151;
    font-size:14px;
}

div.event span {
    opacity: 0.5;
}

.user-settings {
    width: 90px;
    height: 70px;
    background-color: #c9cdcf !important;
}

.user-settings-image {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
}

    .user-settings-image:after {
        position: absolute;
        bottom: 0px;
        left: 0px;
        right: 0px;
        top: 25%;
        text-align: center;
    }

/**
    reference-building-insulation
*/
.reference-building-insulation-parent {
    margin-bottom: 30px;
}
.reference-building-insulation-label {
    color: rgba(0,0,0,0.54);
    font-size: 10px;
}
.reference-building-insulation-container {
    align-items: center;
}
.reference-building-insulation-select {
    margin-top: 0 !important;
    margin-left: 10px !important;
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    min-width: 100px;
}
.horizontal-center {
    text-align: center;
}
.no-margins {
    margin: 0;
}

.action-bar {
    right: 0px;
    left: 350px;
    top: 54px;
    z-index: 998;
    position: fixed;
    padding: 6px 4px 4px 4px;
    margin-bottom: 6px;
    border-bottom: 8px solid #ececec;
    background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiA/PjxzdmcgeG1sbnM9Imh0d…0iMSIgaGVpZ2h0PSIxIiBmaWxsPSJ1cmwoI2xlc3NoYXQtZ2VuZXJhdGVkKSIgLz48L3N2Zz4=);
    background-image: -webkit-linear-gradient(left,#fff,#f3f3f3);
    background-image: -moz-linear-gradient(left,#fff,#f3f3f3);
    background-image: -o-linear-gradient(left,#fff,#f3f3f3);
    background-image: linear-gradient(to right,#fff,#f3f3f3);
    -webkit-box-shadow: 0px 2px 4px #b1b1b1;
    -moz-box-shadow: 0px 2px 4px #b1b1b1;
    box-shadow: 0px 2px 4px #b1b1b1;
}

.action-desc {
    line-height: 10px;
    display: block;
    padding: 0 5px;
    font-size: 10px;
    color: #a5a5a5;
}

.action-bar-date-filter-wrapper {
    background-color: var(--widget-background-grey) !important;
    margin-left:10px;
    padding:0 10px;

    border-radius: 10px;
    background: none;
}
.action-bar-date-filter-label {
    font-size:small;
    opacity:0.4;
    display:inline-block;
}
.action-bar-date-filter-wrapper .date-range-picker {
    display:inline-block;
}



.fa-caret-right-icon:before {
    content: "\f0da"; /* FontAwesome char code for caret right button (>) */
    font-family: FontAwesome;
    padding-right: 8px;
}

.fa-caret-down-icon:before {
    content: "\f0d7"; /* FontAwesome char code for caret down button (v) */
    font-family: FontAwesome;
    padding-right: 8px;
}

.btn-circle.btn-lg {
    width: 35px;
    height: 35px;
    padding: 2px 1px;
    font-size: 18px;
    line-height: 1.33;
    border-radius: 25px;
}

.btn-circle {
    width: 50px;
    height: 50px;
    text-align: center;
    padding: 6px 0;
    font-size: 12px;
    line-height: 1.428571429;
    border-radius: 15px;
}

.btn-lg {
    padding: 10px 16px;
    font-size: 18px;
    line-height: 1.33;
    border-radius: 6px;
}


.bg-warning {
    background-color: #f4b400 !important;
}

.bg-green {
    background-color: green;
}

.dropdown-menu.dropdown-notifications li {
    border-bottom: 1px solid #f3f3f3;
}

.label, .badge {
    display: inline-block;
    font-weight: 300;
    text-shadow: none !important;
}

.rd-display-created-modified {
    font-size: 12px;
    color: #b1b1b1;
}
.widget-pager {
    padding: 0 0 10px 10px;
    font-size: 14px;
    color: #616161;
}

/* UploadCare Overrides */
.upload-widget {
}

.uploadcare-widget-button-remove:before {
    font-family: FontAwesome;
    content: "\f00d";
}

.uploadcare-widget-button-remove {
    padding: .3em .4em;
}

.uploadcare-widget-button-open {
    margin-top: 3px;
}

.uploadcare-widget-dragndrop-area {
    margin-top: -15.3em !important;
    line-height: 15.6 !important;
    width: 150px !important;
}

div.attachment {
    clear: left;
    position: relative;
}

    div.attachment img {
        display: inline-block;
    }

ul.attachments li {
    height: auto;
    margin: 3px;
    padding: 6px;
    border-bottom: 1px solid #f1f1f1;
}

    ul.attachments li:hover {
        background-color: #d1d1d1;
    }

span.att-noimage {
    font-size: 40px;
    padding: 10px 0;
    color: lightblue;
    vertical-align: middle;
    display: inline-block;
}

div.attachment span {
    word-wrap: break-word;
    padding-left: 10px;
    vertical-align: middle;
    display: inline-block;
}

div.attachment button.button-fa-del {
    position: absolute;
    top: -2px;
    right: 0px;
}

div.attachment button.button-fa-download {
    position: absolute;
    bottom: -2px;
    right: 0px;
}

.fr {
    float: right;
}

.margin-left-74 {
    margin-left: 74px !important;
}

.margin-left-10 {
    margin-left: 10px !important;
}

.primary-text {
    display: inline-block;
    font-weight: bold;
}

.push-down-10 {
    margin-bottom: 10px !important;
}

.bgyellow {
    background: #ffe795 !important;
    font-weight: bold !important;
}

.full-height {
    height:100%;
}

.hint {
    font-size:smaller;
}

.stamp-placed {
    background: url('../content/images/Rubber_Stamp-Green.png') no-repeat fixed center;
    background-size: contain;
    /* Lower a bit from 36px as image has white-space */
    width: 36px;
    height: 29px;
    cursor: pointer;
}
.stamp-not-placed {
    background: url('../content/images/Rubber_Stamp-Grey.png') no-repeat fixed center;
    background-size: contain;
    /* Lower a bit from 36px as image has white-space */
    width: 36px;
    height: 29px;
    cursor: pointer;
}
.opacity-0 {
    opacity: 0;
}

.stamp-drawing-detail input,
.stamp-drawing-detail md-checkbox div span,
.stamp-drawing-detail md-select {
    font-size: 14px;
}
/* Heading */
.stamp-drawing-detail label {
    font-size: 18px;
}

.draggable {
    cursor:move;
}

.compliance-achieved label,
.compliance-achieved md-select-value {
    color:green !important;
    font-weight: bold;
}


/* Query Builder and Summary */
div.query-summary,
div.query-summary span,
div.query-summary span span {
    font-family: sans-serif !important;
    font-size: 16px;
}

div.query-summary {
    padding: 12px 0 6px 5px;
}

div.query-sum-line {
    display: inline-block;
}

    div.query-sum-line:hover {
        text-decoration: line-through;
    }

span.query-val {
    font-weight: bold;
}

span.query-field {
    color: dodgerblue;
}

span.data-label span {
    opacity: 0.7;
}

/* Maps */
.clusterimg {
    height: 16px;
    width: 16px;
}

.clusterimg1 {
    height: 12px;
    width: 12px;
}

.markerimg {
    height: 8px;
    width: 8px;
}

.import-wrapper {
     margin-top: 8px;
     width: 100%;
     height: 262px;
     overflow-y: scroll;
}

div.import-hov {
    display: inline;
}
div.import-hov :hover {
    cursor: pointer;
}

.import-dialog {
    height: 350px;
}

.import-span {
    display: block;
    min-height: 22px;
}

.import-bord {
    border-spacing: 2px;
    border-color: grey;
    margin: 0 !important;
    padding: 0 !important;
}

.import-plus-minus {
    padding-right: 8px;
}

.import-toolbar {
    max-height: 64px !important;
    align-items: flex-start;
    align-content: center;
    vertical-align: central;
    padding: 8px;
}

.import-bottom {
    overflow-y: visible;
}

.import-top {
    margin-bottom: 0;
}

.import-indent-1 {
    padding-left: 8px;
}

.import-indent-2 {
    padding-left: 16px;
}

.import-indent-3 {
    padding-left: 24px;
}

.tabMapLegend label {
    text-align: center;
    font-family: 'Comic Sans MS';
    font-size: 10px;
    color: darkslategray;
    font-weight: 500;
}

.checkbox-radio-padding {
    padding-top: 5px !important;
}

.infoWindowContent {
    font-size: 14px !important;
    border-top: 1px solid #ccc;
    padding-top: 10px;
}

/* Label colors */

.label.label-success,
.badge.badge-success {
    background: #77c76d !important;
}

.label.label-warning,
.badge.badge-warning {
    background: #f1b622 !important;
}

.label.label-important,
.badge.badge-important {
    background: #fa3031 !important;
}

.label.label-info,
.badge.badge-info {
    background: #52b9e9 !important;
}

/* $mdDialog - fullscreen dialog styling for mobile */
@media screen and (max-width: 760px){

       .md-dialog-fullscreen {
       height:100%;
       width:100%;
       max-height:100%;
       max-width: 100%;
       position: absolute;
       top: 0;
       left: 0;
       border-radius: 0;
   }
}

/* Navigation Menu */
.navbar-fixed {
    position: fixed;
    top: 0;
    width: 100%;
    background-color: #343434 !important;
    z-index: 50;
}
section.content.full-height {
    padding-top:80px;
    padding-bottom:35px;
}
.lr-sticky-header {
    z-index: 3;
}
.md-button.nav-button {
    height:100%;
    margin-top: 0;
    margin-bottom: 0;
    color:#949494;
    min-width: 60px;
}
.fixed-action-bar {
    position: fixed;
    bottom: 0px;
    left: 0;
    width: 100%;
    background-color: transparent;
    padding-left: 20px;
}

.nav-button.md-button.md-button.md-primary:not([disabled]) md-icon md-icon {
    color:#949494;
}

.nav-menu-section {
    margin: auto;
    max-width: 1810px;
    padding: 4px 18px 7px 0;
}
.nav-menu-section > div:not(:first-child) {
    border-right: 1px solid #444444;
}

.nav-image-div {
    height:25px;
    margin-top:10px;
    text-align: center;

}
.nav-text {
    height:auto;
}

md-menu-content.nav-menu-content {
    max-height: 600px;
    min-width: 250px;
}

a.list-select {
    font-size:10px;
    padding: 0;
    margin: 0;
}

/* Group Buttons*/
.button-group .md-button {
    font-size: 16px;
    margin: 20px 0;
    padding: 3px 15px 3px 15px;
    color: rgb(49, 46, 46);
    background-color: rgba(224, 224, 224, 0.96);
    text-transform: none;
    font-weight: 400;
    min-width:50px;
}

.menu-scrollable {
    overflow-y: scroll;
    overflow-x: visible;
}

.menu-button-item {
    font-size: 14px;
    align-items: center;
    text-align: left;
    text-transform: uppercase;
    cursor: pointer;
    padding-left: 10px;
    padding-right: 10px;
}

.menu-button-item > i {
    margin-left: 0px;
    padding-left: 0px;
    padding-right: 14px;
    margin-left: -4px;
}

.menu-button-nav {
    font-size: 14px;
    align-items: center;
    text-align: left;
    text-transform: uppercase;
    cursor: pointer;
    padding-left: 0px;
    padding-right: 10px;
}

.menu-button-nav > i {
    margin-left: 0px;
    padding-left: 0px;
    padding-right: 14px;
    margin-left: -4px;
}

.home-button {
    margin: auto;
    height: auto;
    padding: 0;
    align-items: center;
    max-height: 100%;
}

.action-filters-mobile,
.action-buttons-mobile {
    display:none;
}
.action-button-desktop.md-button {
    display:block;
}

.md-button.md-raised[disabled] {
    background-color:#e0e0e0;
}

/* Menu Shrink on Mobile Tablet */
@media screen and (max-width: 1400px) {
    .navbar-logo,
    .nav-image-div,
    .user-icon {
        display:none !important;
    }
    .navbar-fixed {
        font-size: 12px !important;
    }
    .nav-button {
        font-size: 12px !important;
        padding: 0;
    }

    .md-toolbar-tools .button-group .md-button {
        font-size: 12px;
        padding: 3px 5px;
    }
    .action-filters-mobile,
    .action-buttons-mobile {
        display:flex;
    }
    .action-filters-desktop,
    .action-button-desktop,
    .action-button-desktop .md-button,
    .action-button-desktop.md-button {
        display:none !important;
    }
    .md-button.is-active {
        font-weight: bold ;
    }
}

.md-button.new-left {
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
}

.md-button.new-right {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
}

.md-button.left {
    border-radius: 10px 0 0 10px;
}

.md-button.middle {
    border-radius: 0;
    border-left: 1px solid rgba(230, 230, 230, 0.96);
    border-right: 1px solid rgba(230, 230, 230, 0.96);
}

.md-button.right {
    border-radius: 0 10px 10px 0;
}

.md-button.md-small {
    min-width:30px;
}

.button-group .md-button:not([disabled]):hover {
    background-color: var(--thermarate-green);
    /* color: rgba(44, 65, 164, 0.96); */
    transition: 0.3s;
}

.button-group .is-active {
    background-color: var(--thermarate-green);
    /* color: rgba(44, 65, 164, 0.96); */
    transition: 0.3s;
}

/* #region responsie table */
.shadow-z-1 {
  -webkit-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 1px 2px 0 rgba(0, 0, 0, 0.24);
  -moz-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 1px 2px 0 rgba(0, 0, 0, 0.24);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 1px 2px 0 rgba(0, 0, 0, 0.24);
}
/* -- Material Design Table style -------------- */
.table {
  width: 100%;
  max-width: 100%;
  margin-bottom: 2rem;
  background-color: #ffffff;
}
.table > thead > tr,
.table > tbody > tr,
.table > tfoot > tr {
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
  text-align: left;
  padding: 1.6rem;
  vertical-align: top;
  border-top: 0;
  -webkit-transition: all 0.3s ease;
  -o-transition: all 0.3s ease;
  transition: all 0.3s ease;
}
.table > thead > tr > th {
  font-weight: 400;
  color: #757575;
  vertical-align: bottom;
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}
.table > caption + thead > tr:first-child > th,
.table > colgroup + thead > tr:first-child > th,
.table > thead:first-child > tr:first-child > th,
.table > caption + thead > tr:first-child > td,
.table > colgroup + thead > tr:first-child > td,
.table > thead:first-child > tr:first-child > td {
  border-top: 0;
}
.table > tbody + tbody {
  border-top: 1px solid rgba(0, 0, 0, 0.12);
}
.table .table {
  background-color: #ffffff;
}
.table .no-border {
  border: 0;
}

.table > tbody > tr > td {
    vertical-align: middle;
}
.table-condensed > thead > tr > th,
.table-condensed > tbody > tr > th,
.table-condensed > tfoot > tr > th,
.table-condensed > thead > tr > td,
.table-condensed > tbody > tr > td,
.table-condensed > tfoot > tr > td {
  padding: 0.8rem;
}

.table-very-condensed > thead > tr > th,
.table-very-condensed > tbody > tr > th,
.table-very-condensed > tfoot > tr > th {
    padding: 0.8rem;
}
.table-very-condensed > thead > tr > td,
.table-very-condensed > tbody > tr > td,
.table-very-condensed > tfoot > tr > td {
    padding: 0;
}

.table-bordered {
  border: 0;
}
.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td {
  border: 0;
  border-bottom: 1px solid #e0e0e0;
}
.table-bordered > thead > tr > th,
.table-bordered > thead > tr > td {
  border-bottom-width: 2px;
}
.table-striped > tbody > tr:nth-child(odd) > td,
.table-striped > tbody > tr:nth-child(odd) > th {
    background-color: #eeeeee;
}
.table-hover > tbody > tr:hover > td,
.table-hover > tbody > tr:hover > th {
  background-color: rgba(0, 0, 0, 0.12);
}
.table-striped-red > td {
    background-color: #f7dede !important;
}
.table-striped-red > td > a > span {
    color: #c34a4a !important;
}
.table-striped-red-light > td {
    background-color: #f7dede !important;
}
.table-striped-red-light > td > a > span {
    color: #c34a4a !important;
}

@media screen and (max-width: 768px) {
  .table-responsive-vertical > .table {
    margin-bottom: 0;
    background-color: transparent;
  }
  .table-responsive-vertical > .table > thead,
  .table-responsive-vertical > .table > tfoot {
    display: none;
  }
  .table-responsive-vertical > .table > tbody {
    display: block;
  }
  .table-responsive-vertical > .table > tbody > tr {
    display: block;
    border: 1px solid #e0e0e0;
    border-radius: 2px;
    margin-bottom: 1.6rem;
  }
  .table-responsive-vertical > .table > tbody > tr > td {
    background-color: #ffffff;
    display: block;
    vertical-align: middle;
    text-align: right;
  }
  .table-responsive-vertical > .table > tbody > tr > td[data-title]:before {
    content: attr(data-title);
    float: left;
    font-size: inherit;
    font-weight: 400;
    color: #757575;
  }
  .table-responsive-vertical.shadow-z-1 {
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
  }
  .table-responsive-vertical.shadow-z-1 > .table > tbody > tr {
    border: none;
    -webkit-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 1px 2px 0 rgba(0, 0, 0, 0.24);
    -moz-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 1px 2px 0 rgba(0, 0, 0, 0.24);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 1px 2px 0 rgba(0, 0, 0, 0.24);
  }
  .table-responsive-vertical > .table-bordered {
    border: 0;
  }
  .table-responsive-vertical > .table-bordered > tbody > tr > td {
    border: 0;
    border-bottom: 1px solid #e0e0e0;
  }
  .table-responsive-vertical > .table-bordered > tbody > tr > td:last-child {
    border-bottom: 0;
  }
  .table-responsive-vertical > .table-striped > tbody > tr > td,
  .table-responsive-vertical > .table-striped > tbody > tr:nth-child(odd) {
    background-color: #ffffff;
  }
  .table-responsive-vertical > .table-striped > tbody > tr > td:nth-child(odd) {
    background-color: #eeeeee;
  }
  .table-responsive-vertical > .table-hover > tbody > tr:hover > td,
  .table-responsive-vertical > .table-hover > tbody > tr:hover {
    background-color: #ffffff;
  }
  .table-responsive-vertical > .table-hover > tbody > tr > td:hover {
    background-color: rgba(0, 0, 0, 0.12);
  }
}

/* #endregion */

/*.lr-drop-target-after {
    border-bottom: 2px solid orange;
}

.lr-drop-target-before {
    border-top: 2px solid orange;
}

    .lr-drop-target-after:before, .lr-drop-target-before:before {
        position: absolute;
        content: '';
        border: 5px solid transparent;
        border-left-color: orange;
        display: inline-block;
    }

.lr-drop-target-after:before {
    z-index: 10;
    left: -5px;
    bottom: -6px;
}

.lr-drop-target-before:before {
    z-index: 10;
    left: -5px;
    top: -6px;
}

.lr-drop-target-after:after, .lr-drop-target-before:after {
    position: absolute;
    content: '';
    border: 5px solid transparent;
    border-right-color: orange;
    display: inline-block;
}

.lr-drop-target-after:after {
    right: -5px;
    bottom: -6px;
}

.lr-drop-target-before:after {
    right: -5px;
    top: -6px;
}*/

table.compliance-table tbody > tr > td:not(.compliance-description),
table.compliance-table thead > tr > th:not(.compliance-description) {
    max-width: 50px;
}

table.compliance-table .compliance-description {
    width: 20%;
}

table.compliance-table tr > td:nth-child(2),
table.compliance-table tr > th:nth-child(2) {
    width: 50px
}

table.compliance-table tr > td:nth-child(1),
table.compliance-table tr > th:nth-child(1) {
    width: 30px
}

table.compliance-table > tbody > tr > td, table.compliance-table > tbody > tr > th, table.compliance-table > tfoot > tr > td, table.compliance-table > tfoot > tr > th, table.compliance-table > thead > tr > td, table.compliance-table > thead > tr > th {
    padding: .4rem;
}

.md-button.md-icon-button.small-icon-button {
    margin: 6px 0px;
    height: 30px;
    line-height: 15px;
    padding: 4px;
    width: 30px;
    border-radius: 50%;
    min-height: 30px;
    min-width: 30px;
}
.small-icon-button .material-icons {
    font-size: 16px;
}
.md-button.md-small.small-icon-button {
    height: 30px;
    width: 20px;
    min-height: 30px;
    min-width: 20px;
}

.small-icon-button span {
    font-size: 11px;
}

.small-compressed md-input-container,
md-input-container.small-compressed {
    padding: 0;
    font-size: 11px;
}

.small-compressed md-button,
.small-compressed button span {
    margin: 0;
    font-size: 11px;
}


/* Hide short text by default (resolution > 1200px) */
.short-text {
    display: none;
}

/* When resolution <= given, hide full text and show short text */
@media (max-width: 1366px) {

    .short-text {
        display: inline-block;
    }

    .full-text {
        display: none;
    }
}

/*
    Vertically condense input fields for when higher density is desired.
*/
.vertically-condensed,
.vertically-condensed md-input-container,
.vertically-condensed layout > md-input-container {
    line-height: 1.0;
    margin-top: 0;
    margin-bottom: 0;
    padding-top: 0;
    padding-bottom: 0;
}

.vertically-condensed .md-errors-spacer {
    min-height: 23px;
}

.vertically-condensed md-select,
.vertically-condensed md-input-container > md-select,
.vertically-condensed md-input-container > md-radio-group,
.vertically-condensed layout > md-input-container {
    line-height: 1.2;
    margin-top: 0;
    margin-bottom: 25px;
    padding-top: 0;
    padding-bottom: 0px;
}

/* Used atm to condense file upload dialog */
.vertically-condensed-ex md-input-container > md-select,
.vertically-condensed-ex md-input-container > md-select > select,
.vertically-condensed-ex div > md-input-container {
    display: flex;
    align-self: center;
    vertical-align: central;
    margin: 0 0;
}

md-input-container.vertically-condensed-ex {
    margin: 0 0;
}

.vertically-condensed-m {
    padding-top: 18px;
    padding-bottom: 12px;
}

.checkbox-aligner {
    line-height: 2;
    align-content: center;
    display: grid;
}

.checkbox-height-fix {
    height: 25px;
    margin: 0px;
}

.md-input-container {
    margin: 0;
    padding: 0;
}

.compliance-option {
    margin: 8px 8px;
    padding: 10px;
}

.compliance-option-hover {
    margin: 8px 8px;
    padding: 10px;
}

.compliance-option-hover:hover {
    border: 1px solid rgb(139,195,74);
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.05), 0px 0px 8px rgba(139,195,74, 0.6);
    margin: 7px 7px;
}

compliance-option-uncompliant-hover {
    margin: 8px 8px;
    padding: 10px;
}

.compliance-option-uncompliant-hover:hover {
    border: 1px solid rgb(229,135,74);
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.05), 0px 0px 8px rgba(229,145,74, 0.6);
    margin: 7px 7px;
}


.compliance-option-selected {
    border: 1px solid rgb(139,195,74);
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.05), 0px 0px 8px rgba(139,195,74, 0.6);
    margin: 7px 7px;
}

.compliance-option-fade {
    opacity: 0.3
}


/* Help with aligning and minimizing size of table data in compliance option rows */
.compliance-row-table-input {
    text-align: center;
    display: flex;
    width: 50px;
    min-width: 50px;
    max-width: 50px;
}

.compliance-row-table-input-m {
    text-align: center;
    display: flex;
}

.kindly-remove-error-spacer > .md-errors-spacer,
.kindly-remove-error-spacer > div > .md-errors-spacer {
    height: 0px;
    display: none;
}


.horizontally-condensed,
.horizontally-condensed md-input-container,
.horizontally-condensed layout > md-input-container
.horizontally-condensed layout > md-input-container > md-select > md-select-value,
.horizontally-condensed > md-select > md-select-value {
    line-height: 1.0;
    margin-left: 0px;
    padding-left: 0px;
    margin-right: 0px;
    padding-right: 0px;
    min-width: 0px;
}


    .horizontally-condensed md-select,
    .horizontally-condensed md-input-container > md-select,
    .horizontally-condensed md-input-container > md-radio-group,
    .horizontally-condensed layout > md-input-container {
        line-height: 1.0;
        margin-left: 0px;
        padding-left: 0px;
        margin-right: 0px;
        padding-right: 0px;
        min-width: 0px;
    }

/*
    For an undiscovered reason which had all of the brains trust investigating, in certain components
    the autocomplete and md-input-container components were greyed out even when NOT disabled.
    After unsuccessfully solving the problem properly, these hacks will have to suffice.
*/

/* Use for md-autocomplete */
.autocomplete-black > md-autocomplete-wrap > md-input-container > input {
    color: #212121 !important;
}

/* Use for md-input-container. */
.input-black > input,
.input-black > div > div > input,
.input-black > md-autocomplete-wrap > md-input-container > input{
    color: #212121 !important;
}


/* Used to make all the filters on our home and job-list tables fit. */
.mini-filters,
.mini-filters > md-toolbar > div,
.mini-filters > md-toolbar > div > div > button,
.mini-filters > md-toolbar > div > div > section > button,
.mini-filters > md-toolbar > div > div > div > section > button {
    font-size: 14px !important;
    padding: 5px 7px !important;
}

/* Gives a subtle(ish) green glow to elements. Recommend to use with animation effect. */
.glowing {
    border: 1px solid rgb(139,195,74);
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.05), 0px 0px 8px rgba(139,195,74, 0.3);
}

/* Used for input elements on particular tables that need to be 'spreadsheet-like' and not have heavy material-design */
.lightweight,
.lightweight:focus,
.lightweight:focus-visible,
.lightweight:disabled {
    border: none;
    background: none;
    outline: none;
    display: block;
}

.lightweight:disabled,
.lightweight:disabled > md-autocomplete-wrap,
.lightweight:disabled > md-autocomplete-wrap > input {
    color: grey;
}

.lightweight.disabled,
.lightweight.disabled > md-autocomplete-wrap > input {
    color: grey;
    cursor: default;
}

.lightweight > md-autocomplete-wrap,
.lightweight > md-autocomplete-wrap > input {
    border: none;
    box-shadow: none;
    background: none;
}

.construction-parent-body {
    background-color: #FAFAFA;
    border-radius: 6px;
    padding: 3px 12px;
    margin-bottom: 6px;
    border: 1px solid transparent;
    transition: 300ms;
    min-height: 91px;
    display: grid;
}


.construction-parent-body:hover {
    border: 1px solid rgb(139,195,74);
    box-shadow: 0px 1px 3px rgba(0, 0, 0, 0.05), 0px 0px 8px rgba(139,195,74, 0.3);
}

.construction-parent-header {
    display: grid;
    grid-template-columns: 540px 1fr;
    align-items: center;
    justify-items: left;
    padding: 10px 0;
    /*width:fit-content;*/
}

.construction-parent-header .divider-both {

  border-left: .1rem solid rgba(0,0,0, 0.2);
  width: 100%;
  row-gap: 10px;
}

.construction-parent-header span.header {
    font-weight: normal;
    font-size: 12px;
    text-align: center;
}

.construction-parent-header-table th > *,
.construction-parent-header-table td > * {
    margin: auto;
}

.construction-parent-header-table .manufacturer,
.construction-parent-header-table .manufacturer * {
    width: 150px;
}

.construction-parent-header-table .frame,
.construction-parent-header-table .frame > * {
    width: 120px;
}

.opening-style,
.opening-style > *
.opening-style md-select-value {
    width: 140px;
    margin: 0 auto;

}

.opening-style-xl,
.opening-style-xl > *,
.opening-style-xl md-select-value {
    width: 180px;
    margin: 0 auto;
}

.opening-style md-select-value,
.opening-style md-select[disabled] md-select-value {
    border: none;
    border-bottom-color: transparent;
    background: none;
}

.service-control-device-style,
.service-control-device-style > *,
.service-control-device-style md-select-value {
    max-width: initial;
    min-width: 240px;
    margin: auto;

}

.construction-parent-header-table .colour,
.construction-parent-header-table .colour > * {
    width: 70px;
}

.construction-parent-header-table .solar,
.construction-parent-header-table .solar * {
    width: 70px;
}

.construction-parent-header-table .glass,
.construction-parent-header-table .glass * {
    width: 130px;
}

.construction-parent-header-table .uvalue,
.construction-parent-header-table .uvalue * {
    width: 60px;
}

.construction-parent-header-table .shgc,
.construction-parent-header-table .shgc * {
    width: 80px;
}

.construction-parent-header-table .area,
.construction-parent-header-table .area *{
    width: 80px;
}


/*.construction-parent-header-table td *,
.construction-parent-header-table th * {
    display: contents;
}*/

.construction-parent-header .lightweight,
.construction-parent-header .lightweight > md-autocomplete-wrap > input {
    font-weight: bold;
    font-size: 14px;
    text-align: center;
    background-color: transparent;
}

.construction-parent-header .lightweight {
    background-color: transparent !important;
    background: none !important;
}

.construction-parent-table {
    text-align: center;
}

.construction-parent-title {
    display: inline-block;
    width: 100%;
    font-size: 18px !important;
    text-align: left !important;
    font-weight: bold;
}


.elements-body {
    background-color: white;
    border-radius: 4px;
    margin-top: 10px;
    padding: 16px;

    display: grid;
    grid-template-columns: 100fr 1fr;
    justify-content: center;
    justify-items: stretch;
}

.elements-body .elements-header {
    transform: rotate(180deg);
    writing-mode: vertical-rl;
    text-align: center;

    font-size: 16px;
    color: lightgray;
}

.elements-table {
    border-collapse: collapse;
    table-layout: fixed;
}

.elements-table td > *,
.elements-table th > * {
    margin: auto;
}

.elements-table .element,
.elements-table .element * {
    width: 50px;
}

.elements-table .parent,
.elements-table .parent > * {

}

.elements-table .storey,
.elements-table .storey > * {
    width: 180px;
}

.elements-table .boolean-cell,
.elements-table .boolean-cell > * {
    width: 130px;
}

.elements-table .numeric-cell.small,
.elements-table .numeric-cell.small * {
    width: 70px;
}

.elements-table .numeric-cell,
.elements-table .numeric-cell * {
    width: 110px;
}

.elements-table .action-cell {
    width: 80px;
}

.elements-table > thead > tr > th,
.elements-table > tbody > tr > td {
    font-size: 12px;
    font-weight: normal;
    padding: 6px 0;
    text-align: center;
}

.elements-table > tbody > tr:hover {
    background-color: #fcfcfc;
}

.elements-table > thead > tr > th {
    border-bottom: 1px solid lightgrey;
}

.elements-table > tbody > tr:not(:last-child) > td {
    border-bottom: 1px solid rgba(0,0,0,0.06);
}

.elements-table > tbody > tr > td,
.elements-table > tbody > tr > td > input,
.elements-table > tbody > tr > td > md-select > md-select-value {
    background: none;
    border: none;
    border-bottom-style: none;
    border-bottom: none;
    text-align: center;
    font-size: 14px;

}

.elements-table > tbody > tr > td > input {
    width: 80px;
}

.elements-table > tbody > tr > td > md-select,
.elements-table > tbody > tr > td > md-select > md-select-value
{
    min-width: 60px;
    max-width: 340px;
    text-align: center;
    margin: auto !important;
}

.elements-table-padding-l > thead > tr > th,
.elements-table-padding-l > tbody > tr > td {
    padding: 6px 14px;
}

.service-header {
    display: grid;
    grid-template-columns: 420px 1fr 160px;
    align-items: center;
    justify-items: left;
    padding: 10px 0;
}

.service-header .divider-both {
    border-right: .2rem solid rgba(0,0,0, 0.2);
    border-left: .2rem solid rgba(0,0,0, 0.2);
    width: 100%;
}


.feather-icon-button {
    border: none;
    background-color: transparent;
    outline: none;
}

.feather-icon-button:disabled > img {
    opacity: 50%;
}

.feather-icon-button.sm {

}

.nav-icon-alt {
    height: 30px;
}

.lightweight-button {
    border: none;
    background-color: rgba(0, 0, 0, 0.03);
    outline: none;
    padding: 12px 20px;

    border-radius: 6px;

    transition: 300ms;
}

.lightweight-button:hover {
    background-color: rgba(0, 0, 0, 0.06);
}

.clickable {
    cursor: pointer;
}


.service-manufacturer-header {
    font-size: 9px;
    color: grey;
    margin-left: 3px;
}

.service-manufacturer-input {
    font-weight: bold;
    font-size: 16px;
}


.assessment-notes-input {
    padding: 8px 8px;
    min-height: 83px;
    width: -webkit-fill-available;

    border: 2px solid #59814923;
    border-radius: 6px;
    outline: none;

    resize: vertical;
}

.assessment-notes:focus-visible {
    border: 2px solid rgba(129, 98, 73, 0.14);
}


.center-input, md-datepicker.center-input div input {
    margin: auto;
    text-align: center;
}

/* When centering on a select move the arrow to the right so actually looks centered */
md-select.center-input .md-select-value .md-select-icon {
    width: 7px;
}

.overridable {
    cursor: pointer;
    text-decoration: underline;
}

.overridden {
    color: orange;
    font-weight: bold;
}

/* Basically removes all styling from md-select. */
.widget-action-bar-select-filter,
.widget-action-bar-select-filter:focus {

    width: 240px;
    height: 33px;
    border: none;
    border-radius: 10px;

    margin: 0 16px 0 0;
    margin-bottom: 2px;
    padding: 2px 6px;
    font-size: 15px;

    background: rgba(0, 0, 0, 0.05);
    outline: none;
    display: block;
}


.widget-action-bar-select-filter md-select-value,
.widget-action-bar-select-filter:focus md-select-value {
    /* */
    border: none !important;
}

.table-clickable {
    cursor: pointer;
    text-decoration: underline;
}

md-toolbar,
.md-menu-toolbar {
    background-color: var(--thermarate-grey) !important;
}

/* This replaces the comma , with a + sign in an <md-select> element with the 'multiple' attribute */
.multi-select-plus-separator[multiple] .md-select-value span:first-child {
    visibility: collapse;
}

.multi-select-plus-separator[multiple] .md-select-value .md-text {
    display: inline-block !important;
    visibility: visible;
}

.multi-select-plus-separator[multiple] .md-select-value .md-text:not(:last-child):after {
    content: '+';
    margin: 0 -5px 0 0;

}

/* Auto Complete */
/* Make smaller */
.small-autocomplete {
    width: 75px;
    max-width: 75px;
    min-width: 75px;
    margin: 10px auto;
}
.small-autocomplete > md-autocomplete-wrap > input {
    text-align: center;
}
/* Remove clear button */
.small-autocomplete > md-autocomplete-wrap > button {
    display: none;
}

/* Home Table Highlighting for the dynamic row colors  */
.table-hover > tbody > tr:hover > td,
.table-hover > tbody > tr:hover > th {
    filter: brightness(0.9);
}

.table-hover > tbody > tr,
.table-hover > tfoot > tr,
.table-hover > thead > tr {
    -webkit-transition: all .3s ease;
    -o-transition: all .3s ease;
    transition: all .3s ease;
}


.busy-processing-overlay {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;

    display: grid;
    align-items: center;
    justify-items: center;
    z-index: 9999;

    background-color: rgba(255, 255, 255, 0.75);
}

.not-applicable-checkbox {
    margin-top: auto;
    margin-bottom: auto;
    margin-right: 20px !important;
}


.md-select-show-all > md-select-menu,
.md-select-show-all > md-select-menu > md-content {
    max-height: none;
}

.bulk-edit-column {
    width: 50px;
}

.more-actions-icon-column {
    width: 30px;
}

.construction-action-modal-search-container {

    background-color: var(--thermarate-grey);
    border-radius: 6px;

    display: grid;
    grid-template-columns: 1fr;
    align-content: start;
    row-gap: 2px;
    padding: 10px 10px;

    max-height: 400px;
    overflow-y: auto;
}

.construction-action-modal-search-item {
    border-radius: 4px;
    padding: 6px 10px;
    transition: 300ms;
}

.construction-action-search-selected {
    background-color: var(--thermarate-green-light);
}

.construction-action-modal-search-item:hover {
    background-color: white;
}

.widget-header-vertically-expand > div.modal-header > md-toolbar > div.md-toolbar-tools {
    max-height: none;
    height: inherit;
    min-height: 64px;
}

.permissions-group {
    background-color: #FAFAFA;
    margin: 5px;
    padding: 10px;
    border-radius: 5px 5px;
}

span .offset-dropdown,
md-option .offset-dropdown {
    margin-left: 24px;
}

table.table-data-centered > thead > tr > th,
table.table-data-centered > tbody > tr > td {
    text-align: center;
}

.reorderable-grid-item {
    border: 2px solid transparent;
    border-radius: 3px;
    width: 400px;
}

.reorderable-grid-item.lr-drop-target-before,
.reorderable-grid-item.lr-drop-target-after {
    border: 2px solid var(--thermarate-green);
}

/* Stop dropdown label from moving */
.md-select-value > span:first-child {
    transform: none !important;
    -webkit-transform: none !important;
}

.energy-labs-sort .sort-container {
    margin: 32px 20px 25px 20px;
    justify-content: flex-end;
    color: black;
}

/* Add 'Share' icon to dropdown options */
.custom-dropdown-option {
    position: relative;
}
.custom-dropdown-option > .copy-across-button {
    width: 15px;
    height: 15px;
    padding: 6px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 15px;
    border-radius: 50%;
    cursor: pointer;
}
.custom-dropdown-option > .copy-across-button:hover {
    background-color: #bbbbbb;
}
.custom-dropdown-option > .copy-across-button > img {
    width: 100%;
    height: 100%;
}

.bulk-edit-table-consistent-heights tr {
    height: 50px;
}