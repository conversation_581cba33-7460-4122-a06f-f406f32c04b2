(function () {
    'use strict';

    var app = angular.module('app');
    // Widget Action Bar directive.
    // Supports creation of an action bar for both lists and forms.
    // Includes Quick Find, New Record Buttons, Other Action Buttons, and Refresh (with spinner).
    // Attributes:
    //    quick-find-model: defines the filter field used by a list
    //    refresh-list: function to call to refresh data
    //    spinner-busy: true/false variable that indicates when to display the spinned (normally vm.isBusy)
    //    new-record: function to call to create a new record (via modal or new page)
    //    new-record-text: text to display on the new record button
    //    action-buttons: a list of objects that defines the action buttons to display
    //                    { name: 'Button Name', onclick: functiontoacall,
    //                      desc: 'More detailed description of the buttons purpose',
    //                      roles: 'comma separated string of roles. if not specified everyone has access',
    //                      condition: 'a function that evaluates to true or false.  If always true have function just return true.'}
    //    filterOptions: an array of filter options to be displayed.  Format [{code: 'Current', name: 'Current Items'},{...}]
    //    filterChanged: function to call when the filter has changed
    app.directive('ccWidgetActionBar', ['$rootScope', '$compile', '$timeout', '$sce', function ($rootScope, $compile, $timeout, $sce) {
        //Usage:
        //<div cc-widget-action-bar quick-find-model='vm.listfilter' action-buttons='vm.actionList' refresh-list='vm.refreshList()' spinner-busy='vm.isBusy' new-record='vm.newRecord()' new-record-text='New Batch' ></div>
        var directive = {
            link: link,
            restrict: 'AE',
            scope: {
                'selectedRecordList': '&',
                'quickFindModel': '=',
                'actionButtons': '=',
                'persistName': '@',
                'refreshList': '&',
                'exportData':'&',
                'spinnerBusy': '=',
                'newRecord': '&',
                'newRecordText': '@',
                'otherRecord': '&',
                'otherRecordText': '@',
                'filterChanged': '&',
                'filterOptions': '=',
                'currentFilter': '=',
                'showAddButton': '=',
                'disabledAddButton': '=',
                'showOtherButton': '=',
                'hide': '=',
                'exportList': '&',
                'exportFilename': '@',
                'isReport': '=',
                'importList': '&',
                'selectedCount': '=',
                'recordTitle': '@',
                'dateSortOptions': '=',
                'dateRanges': '=',
                'dateData': '=',
                'defaultStart': '=',
                'quickFindHolder': '@',
                'quickFindByName': '@',
                'quickFindChange': '&',
                'queryBuilderModel': '=',
                'queryBuilderName': '@',
                'queryBuilderCurrent': '=',
                'filterAOptions': '=',
                'filterATitle': '@',
                'filterBOptions': '=',
                'filterBTitle': '@',
                'filterCOptions': '=',
                'filterCTitle': '@',
                'dropdownList': '=',
                'dropdownListDisplay': '@',
                'dropdownListModel': '=',
                'dropdownListShow': '=',
                'columnOptions': '=',
                'filterColumns': '=',
                'filterColumnsChanged': '=',
                'customFilter': '&',
            },
        };
        return directive;

        let dummyModel = null;

        function link(scope, element, attrs) {

            var wkHtml = "";
            var appendWkHtml = "";
            scope.dropdownModel = { model: {}};
            scope.colNames = "";
            scope.filterSummary = "";
            if (attrs.hide != undefined && scope.hide == true) {
                return;
            }
            if (attrs.quickFindModel != undefined && attrs.quickFindModel != "") {
                if (!attrs.quickFindHolder) {
                    attrs.quickFindHolder = "Quick Find"
                }
                var appendFindByName = "";
                if (attrs.quickFindByName) {
                    /*appendFindByName = ' ng-change="quickFindChange({value: currentFilter})" ';*/
                    scope.$watch(scope.quickFindModel, function (newval, oldval) {
                        scope.quickFindChange({ value: attrs.currentFilter, newval: newval, oldval: oldval });
                    });
                }
                wkHtml = wkHtml + '<div class="widget-search-input-container">' +
                    '<input type="text" ng-model="quickFindModel" ng-model-options="{ updateOn: \'change blur\' }" class="quick-search" data-ng-class="{bgyellow: quickFindModel != \'\' && quickFindModel != null }" title="' + attrs.quickFindHolder + '"  placeholder="' + attrs.quickFindHolder + '" data-rd-persist="'
                            + (attrs.persistName != undefined ? attrs.persistName : $rootScope.currentState.replace("-","_")) + '" ' + appendFindByName + ' />' +
                    '<img src="/content/images/cross.png" class="widget-search-clear-button" ng-show="quickFindModel" ng-click="quickFindModel = null; refreshList(); $event.stopPropagation()">' +
                    '</div>';
            }

            if (attrs.selectedRecordList != undefined && attrs.selectedRecordList != "") {
                wkHtml = wkHtml + '<md-button md-no-ink class="md-primary" ng-click="selectedRecordList()">Select</md-button>';
            }
            if (attrs.newRecord != undefined && attrs.newRecord != "") {
                wkHtml = wkHtml + '<md-button class="" ng-click="newRecord()" data-ng-show="showAddButton" data-ng-disabled="disabledAddButton" title="create a {{newRecordText}}">{{newRecordText}}</md-button>';
            }
            if (attrs.otherRecord != undefined && attrs.otherRecord != "") {
                wkHtml = wkHtml + '<md-button class="" ng-click="otherRecord()" data-ng-show="showOtherButton" title="create a {{otherRecordText}}">{{otherRecordText}}</md-button>';
            }
            if (attrs.exportData != undefined && attrs.exportData != "") {
                wkHtml = wkHtml + '<md-button class=""  ng-click="exportData()">Export</md-button>';
            }
            if (attrs.dropdownList != undefined && attrs.dropdownList != "") {
                var displayExpression = "item";
                if (attrs.dropdownListDisplay != undefined && attrs.dropdownListDisplay != "") {
                    displayExpression += "." + attrs.dropdownListDisplay;
                }
                wkHtml = wkHtml
                    + '<md-input-container class="actionbar-dropdown" ng-if="dropdownListShow">'
                        + '<label></label>'
                        + '<md-select ng-model="dropdownModel.model">'
                            + '<md-option ng-repeat="item in dropdownList" ng-value="item">'
                                + '{{' + displayExpression + '}}'
                            + '</md-option>'
                        + '</md-select>'
                    + '</md-input-container>';
                //If the model is passed into this directive, the ng-model on the md-select does not work.
                //For that reason we use a model on the directive scope, put a watch on it, and assign the value to the passed-in controller scope model whenever it changes.
                //Since the current displayed value is on teh controller scope, the second watch watches the controller model and assigns the current value to this directives scope.
                //-Tim
                scope.$watch('dropdownModel.model', function (newval, oldval) {
                    scope.dropdownListModel = scope.dropdownModel.model;
                });
                scope.$watch('dropdownListModel', function (newVal, oldVal) {
                    scope.dropdownModel.model = scope.dropdownListModel;
                });
            }

            var actionButtonElements = "";
            var actionButtonElementsMobile = "";

            actionButtonElementsMobile = actionButtonElementsMobile + '<md-menu><md-button class="md-icon-button" ng-click="$mdOpenMenu()"><i class="material-icons">more_vert</i></md-button><md-menu-content>';

            if (scope.actionButtons != undefined) {
                var spinClass = "spinner-right";
                if (scope.isReport == true) {
                    spinClass = "spinner-mid-page";
                }

                let buttonsHtml =     ' <md-button ng-repeat="rec in actionButtons"'
                                    + '            ng-disabled="rec.disabled()"'
                                    + '            class="{{rec.class}}" ng-confirm-click="rec.onclick()"'
                                    + '            ng-confirm-condition="{{rec.confirmationMessage != null}}"'
                                    + '            ng-confirm-message="{{rec.confirmationMessage}} {{selectedCount}} {{recordTitle}}."'
                                    + '            ng-show="rec.condition() || rec.condition() == undefined"'
                                    + '            redi-allow-roles="{{rec.roles}}" >'
                                    + '     <i class="{{rec.icon}}"></i>'
                                    + '     <span>{{rec.name}}'
                                    + '         <md-tooltip ng-if="rec.disabled() && rec.tooltip"'
                                    + '                     md-direction="top">'
                                    + '             {{rec.tooltip}}'
                                    + '         </md-tooltip>'
                                    + '     </span>'
                                    + ' </md-button>';

                actionButtonElements = actionButtonElements + buttonsHtml;
                actionButtonElementsMobile = actionButtonElementsMobile + '<div><md-button ng-repeat="rec in actionButtons" ng-disabled="((selectedCount>0||selectedCount==null)?false:true)" class="" ng-confirm-click="rec.onclick()" ng-confirm-condition="{{rec.confirmationMessage != null}}" ng-confirm-message="{{rec.confirmationMessage}} {{selectedCount}} {{recordTitle}}." ng-show="rec.condition() || rec.condition() == undefined" redi-allow-roles="{{rec.roles}}" ><i class="{{rec.icon}}"></i> {{rec.name}}</md-button></div>';

            }
            if (attrs.exportList != undefined && attrs.exportList != "") {
                var wkFilename = 'export';
                if (attrs.exportFilename != undefined && attrs.exportFilename != "") {
                    wkFilename = attrs.exportFilename;
                }
                actionButtonElements = actionButtonElements + '<md-button class="" ng-csv="exportList" csv-header="colNames" filename="' + wkFilename + '.csv" title="export list to a csv file">Export</md-button>';
                actionButtonElementsMobile = actionButtonElementsMobile + '<div><md-button class="" ng-csv="exportList" csv-header="colNames" filename="' + wkFilename + '.csv" title="export list to a csv file">Export</md-button></div>';
            }

            actionButtonElementsMobile = actionButtonElementsMobile + '</md-menu-content></md-menu>';
            var showQueryHtml = "";
            if (attrs.queryBuilderModel != undefined && attrs.queryBuilderModel != null && attrs.queryBuilderModel != "") {
                wkHtml = wkHtml + "<query-builder query-model='queryBuilderModel' show-summary='false' run-query='refreshList()' query-name='queryBuilderName' current-query='queryBuilderCurrent' ></query-builder>";
                showQueryHtml = "<query-summary current-query='queryBuilderCurrent' run-query='refreshList()'></query-summary>"
            }

            if (attrs.refreshList != undefined && attrs.refreshList) {
                var spinClass = "spinner-right";
                if (scope.isReport == true) {
                    spinClass = "spinner-mid-page";
                }
                appendWkHtml = '<div flex-gt-md="none" flex="5" flex-sm="10" flex-xs="30" class="refresh-btn-container"><div class="' + spinClass + '" data-ng-show="spinnerBusy" data-cc-spinner=""></div>' +
                            '<md-button class="md-icon-button full-height" data-ng-show="!spinnerBusy" ng-click="refreshList({value: currentFilter})" title="Refresh"><md-icon class="icon_refresh" aria-label="Refresh data">refresh</md-icon></md-button></div>';
            }

            var filterElements = "";
            var filterElementsMobile = "";

            filterElementsMobile = filterElementsMobile + '<md-menu><md-button class="md-icon-button" ng-click="$mdOpenMenu()"><i class="fa fa-filter"></i></md-button><md-menu-content>';


            if(attrs.filterAOptions?.length > 0 || attrs.filterBOptions?.length > 0 || attrs.filterCOptions?.length > 0) {
                filterElements += `<div class="md-toolbar-tools">`;

                // Filter A Options
                if (attrs.filterAOptions?.length > 0) {

                    filterElements += `
                        <md-select class="widget-action-bar-select-filter"
                                   style="width: 300px"
                                   ng-change="scope.filterA.onclick(scope.filterA.code);"
                                   ng-model="scope.filterA"
                                   ng-model-options="{trackBy: '$value.code'}"
                                   placeholder="{{filterATitle}}: All">
                            <md-option ng-repeat="rec in filterAOptions | filter: { visible: true }"
                                       ng-value="rec">
                                {{filterATitle}}: {{rec.name}}
                            </md-option>
                        </md-select>
                        `;

                    filterElementsMobile = filterElementsMobile + '<md-menu-item class="menu-button-item"><md-menu><md-menu-item ng-click="$mdOpenMenu()" class="menu-button-nav"><i class="material-icons">chevron_left</i>&nbsp;Views</md-menu-item><md-menu-content class="menu-scrollable"><div ng-repeat="rec in filterAOptions"><md-menu-item class="menu-button-item" ng-click="rec.onclick(rec.code)" ng-class="{\'is-active\': filterA==rec.code}" flex>{{rec.name}}</md-menu-item></div></md-menu-content></md-menu></md-menu-item><md-menu-divider></md-menu-divider>';
                }

                // Filter B Options
                if(attrs.filterBOptions?.length > 0) {
                    filterElements += `
                        <md-select class="widget-action-bar-select-filter"
                                   style="width: 300px;"
                                   ng-change="scope.filterB.onclick(scope.filterB.code);"
                                   ng-model="scope.filterB"
                                   ng-model-options="{trackBy: '$value.code'}"
                                   placeholder="{{filterBTitle}}: All">
                            <md-option ng-repeat="rec in filterBOptions | filter: { visible: true }"
                                       ng-value="rec">
                                {{filterBTitle}}: {{rec.name}}
                            </md-option>
                        </md-select>
                        `;
                }

                // Filter C Options
                if(attrs.filterCOptions?.length > 0) {

                    filterElements += `
                        <md-select class="widget-action-bar-select-filter"
                                   style="width: 300px;"
                                   ng-change="scope.filterC.onclick(scope.filterC.code);"
                                   ng-model="scope.filterC"
                                   ng-model-options="{trackBy: '$value.code'}"
                                   placeholder="{{filterCTitle}}: All">
                            <md-option ng-repeat="rec in filterCOptions | filter: { visible: true }"
                                       ng-value="rec">
                                {{filterCTitle}}: {{rec.name}}
                            </md-option>
                        </md-select>
                        `;
                }

                filterElements += "</div>"
            }


            if (attrs.filterOptions != undefined && scope.filterOptions != null && scope.filterOptions.length > 0) {
                if (scope.currentFilter == undefined || scope.currentFilter == null) {
                    scope.currentFilter = scope.filterOptions[0].code;
                }
                filterElements = filterElements + "<div class='filter-dropdown' ng-show='filterOptions && filterOptions.length > 1'><select class='vcentre datasetdrop' ng-model='currentFilter' ng-change='filterChanged({value: currentFilter})' ng-options='rec.code as rec.name for rec in filterOptions'></select></div>"
            }

            var columnFilterElement = "";
            var columnFilterElementMobile = "";

            let filterColumnsSettings = "";

            if (attrs.filtercolumns != undefined && attrs.filtercolumns != "") {


                filterColumnsSettings = `
                    <md-menu style="margin-left: 18px;">
                        <md-button class="md-icon-button" ng-click="$mdOpenMenu()"><i class="material-icons">settings</i></md-button>
                        <md-menu-content>
                            <span class="columnFilter">Select columns to be displayed&nbsp;</span>
                            <div class="columnFilter" ng-repeat="item in columnOptions.columnList track by item.sortOrder">
                                <md-checkbox ng-model="columnOptions[item.reference]">{{item.description}}</md-checkbox>
                            </div>
                        </md-menu-content>
                    </md-menu>`;





                columnFilterElementMobile = columnFilterElementMobile + '<md-menu><md-button class="md-icon-button" ng-click="$mdOpenMenu()"><i class="material-icons">settings</i></md-button><md-menu-content><span class="columnFilter">Select columns to be displayed&nbsp;</span><div class="columnFilter" ng-repeat="item in columnOptions.columnList track by item.sortOrder"><md-checkbox ng-model="columnOptions[item.reference]">{{item.description}}</md-checkbox></div></md-menu-content></md-menu>';

                //TODO watch for changes to filtercolumns
                scope.$watch('columnOptions', function (newVal, oldVal) {
                    scope.filterColumnsChanged(newVal, oldVal)
                }, true);
            }

            if (attrs.customFilter != undefined && attrs.customFilter != "") {
                filterElements = filterElements + '<md-button class="md-icon-button" ng-click="customFilter()"><i class="fa fa-filter"></i></md-button>';
                filterElementsMobile = filterElementsMobile + '<md-menu-item class="menu-button-item"><md-button ng-click="customFilter()"><i class="fa fa-filter"></i>&nbsp;Active Filter</md-button></md-menu-item><md-menu-divider></md-menu-divider>';
            }

            if (scope.dateRanges != undefined && scope.dateRanges != "") {
                filterElements = filterElements + "<div class='action-bar-date-filter-wrapper'>";
                if (scope.dateData != undefined && scope.dateData != "") {

                    filterElements +=
                        `<div class='action-bar-date-filter-label'>
                            <md-select ng-model="dateData.sortBy"
                                       style="width: 110px; background: none;"
                                       ng-change="refreshData()"
                                       class="widget-action-bar-select-filter">
                                <md-option ng-repeat="opt in dateSortOptions"
                                           ng-value="opt">
                                    {{opt}}
                                </md-option>
                            </md-select>
                        </div>`;

                }


                filterElements += `
                    <div rd-date-range-picker
                         class='date-range-picker'
                         ng-model='defaultStart' format='MMMM D, YYYY' ranges='dateRanges'
                         on-change='refreshData' >
                    </div>
                </div>`;


                filterElementsMobile = filterElementsMobile + "<md-menu-item class='menu-button-item'><div rd-date-range-picker class='date-range-picker' ng-model='defaultStart' format='MMMM D, YYYY' ranges='dateRanges' on-change='refreshData' ></div></md-menu-item>";
            }

            filterElementsMobile = filterElementsMobile + '</md-menu-content></md-menu></div>';


            wkHtml = wkHtml + "<div class='filter-picker-wrapper action-filters-desktop' flex>" + actionButtonElements + "<span flex></span>" + filterElements + filterColumnsSettings + "</div><div class='action-filters-mobile' flex><span flex></span>" + actionButtonElementsMobile + columnFilterElementMobile + filterElementsMobile;


            scope.refreshData = function () {
                // Put in a timeout to ensure modified date range is available.
                $timeout(function () {
                    scope.filterChanged({value : scope.currentFilter});
                });
            }

            if (attrs.importList != undefined && attrs.importList != "") {
                wkHtml = wkHtml + '<md-button class="" ng-click="importList()" title="import list from file.">Import</md-button>';
            }
            element.html('<md-toolbar class="widget-actionbar-wrapper" >' + '<div class="md-toolbar-tools widget-actionbar">' + wkHtml + appendWkHtml + '</div></md-toolbar>' + showQueryHtml).show();
            $compile(element.contents())(scope);

            if (attrs.exportList != undefined) {
                scope.$watch(scope.exportList, function () {
                    if (scope.exportList() != undefined && scope.exportList().length > 0 && typeof scope.exportList()[0] === 'object') {
                        var recs = scope.exportList()[0];
                        scope.colNames = Object.keys(recs);
                    }
                });
            }
            if (showQueryHtml != '') {
                scope.$watch("queryBuilderCurrent.summary", function () {
                    if (scope.queryBuilderCurrent != null) {
                        scope.filterSummary = $sce.trustAsHtml(scope.queryBuilderCurrent.summary);
                    }
                });
            }
        }
    }]);
})();